#!/usr/bin/env python3
"""
Simple DOCX Encoding Fixer

Based on the analysis, this script focuses on fixing the specific encoding issues
found in your DOCX files, particularly the Latin-1 characters that should be
Devanagari script.
"""

import os
from pathlib import Path
from docx import Document

def extract_text_from_docx(file_path):
    """Extract all text from a DOCX file"""
    try:
        doc = Document(file_path)
        text_parts = []
        
        # Get text from paragraphs
        for para in doc.paragraphs:
            if para.text.strip():
                text_parts.append(para.text)
        
        # Get text from tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    if cell.text.strip():
                        text_parts.append(cell.text)
        
        return '\n'.join(text_parts)
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return None

def fix_encoding_issues(text):
    """Try to fix common encoding issues"""
    fixes = {}
    
    # Common encoding fixes for Devanagari text
    encoding_pairs = [
        ('latin-1', 'utf-8'),
        ('cp1252', 'utf-8'),
        ('iso-8859-1', 'utf-8'),
    ]
    
    for source_encoding, target_encoding in encoding_pairs:
        try:
            # Try to encode as source then decode as target
            fixed_text = text.encode(source_encoding).decode(target_encoding)
            if fixed_text != text:
                fixes[f"{source_encoding}_to_{target_encoding}"] = fixed_text
        except (UnicodeEncodeError, UnicodeDecodeError):
            continue
    
    return fixes

def create_fixed_docx(original_path, fixed_text, output_path):
    """Create a new DOCX file with fixed text"""
    try:
        # Create new document
        doc = Document()
        
        # Add title
        doc.add_heading(f'Fixed: {original_path.stem}', 0)
        
        # Add note about the fix
        doc.add_paragraph('This document has been processed to fix encoding issues.')
        doc.add_paragraph('Original file: ' + str(original_path))
        doc.add_paragraph('')  # Empty line
        
        # Add fixed text (split by paragraphs)
        paragraphs = fixed_text.split('\n')
        for para_text in paragraphs:
            if para_text.strip():
                doc.add_paragraph(para_text)
        
        # Save the document
        doc.save(output_path)
        print(f"✓ Fixed document saved as: {output_path}")
        return True
    except Exception as e:
        print(f"✗ Error creating fixed document: {e}")
        return False

def analyze_and_fix_file(file_path):
    """Analyze and fix a single DOCX file"""
    print(f"\n{'='*60}")
    print(f"Processing: {file_path.name}")
    print('='*60)
    
    # Extract text
    text = extract_text_from_docx(file_path)
    if not text:
        print("Could not extract text from file.")
        return
    
    # Show sample
    print(f"Sample text (first 200 chars):")
    print(f"'{text[:200]}...'")
    
    # Check for problematic characters
    latin1_chars = [c for c in text if 160 <= ord(c) <= 255]  # Latin-1 supplement
    if latin1_chars:
        unique_latin1 = list(set(latin1_chars))[:10]
        print(f"\nFound {len(latin1_chars)} Latin-1 characters: {unique_latin1}")
        print("This suggests encoding issues that may be fixable.")
        
        # Try fixes
        fixes = fix_encoding_issues(text)
        if fixes:
            print(f"\nFound {len(fixes)} potential fixes:")
            for fix_name, fixed_text in fixes.items():
                print(f"\n--- Fix: {fix_name} ---")
                print(f"Sample: '{fixed_text[:200]}...'")
                
                # Check if this looks better (has Devanagari characters)
                devanagari_chars = [c for c in fixed_text if 0x0900 <= ord(c) <= 0x097F]
                if devanagari_chars:
                    print(f"✓ This fix contains {len(devanagari_chars)} Devanagari characters!")
                    
                    # Auto-save this fix
                    output_name = f"{file_path.stem}_fixed_{fix_name}.docx"
                    if create_fixed_docx(file_path, fixed_text, output_name):
                        print(f"✓ Automatically saved promising fix: {output_name}")
                else:
                    print("✗ This fix doesn't seem to contain Devanagari script.")
        else:
            print("No automatic fixes found.")
    else:
        print("No Latin-1 characters found - file may already be correctly encoded.")

def main():
    """Main function"""
    print("DOCX Encoding Fixer")
    print("===================")
    print("This tool will analyze your DOCX files and attempt to fix encoding issues.")
    print("It will automatically save promising fixes as new files.")
    
    # Find all DOCX files
    docx_files = [f for f in Path('.').glob('*.docx') if not f.name.startswith('~')]  # Skip temp files
    
    if not docx_files:
        print("\nNo DOCX files found in current directory.")
        return
    
    print(f"\nFound {len(docx_files)} DOCX files:")
    for i, file_path in enumerate(docx_files, 1):
        print(f"{i}. {file_path.name}")
    
    # Process each file
    for file_path in docx_files:
        analyze_and_fix_file(file_path)
    
    print(f"\n{'='*60}")
    print("Processing complete!")
    print("Check the generated '*_fixed_*.docx' files to see if the encoding issues are resolved.")
    print("Compare them with the originals in MS Word to verify the fixes.")

if __name__ == "__main__":
    main()
