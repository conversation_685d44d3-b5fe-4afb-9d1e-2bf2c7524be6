#!/usr/bin/env python3
"""
Font and Encoding Fixer for DOCX Files

This tool handles cases where text appears as gibberish due to:
1. Font encoding issues (legacy fonts)
2. Character mapping problems
3. Copy-paste encoding corruption
"""

import os
from pathlib import Path
from docx import Document

def extract_text_from_docx(file_path):
    """Extract all text from a DOCX file"""
    try:
        doc = Document(file_path)
        text_parts = []
        
        for para in doc.paragraphs:
            if para.text.strip():
                text_parts.append(para.text)
        
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    if cell.text.strip():
                        text_parts.append(cell.text)
        
        return '\n'.join(text_parts)
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return None

def analyze_text_issues(text):
    """Analyze what type of encoding/font issues the text has"""
    issues = []
    
    # Check for mixed scripts
    devanagari_count = sum(1 for c in text if 0x0900 <= ord(c) <= 0x097F)
    latin_count = sum(1 for c in text if ord(c) < 128)
    extended_latin_count = sum(1 for c in text if 128 <= ord(c) <= 255)
    other_count = len(text) - devanagari_count - latin_count - extended_latin_count
    
    print(f"Character distribution:")
    print(f"  Devanagari (U+0900-U+097F): {devanagari_count}")
    print(f"  Basic Latin (U+0000-U+007F): {latin_count}")
    print(f"  Extended Latin (U+0080-U+00FF): {extended_latin_count}")
    print(f"  Other Unicode: {other_count}")
    
    # Look for suspicious patterns
    suspicious_patterns = [
        ('ldlt', 'मिति (Date)'),
        ('btf{ g+=', 'पुस्तक नं (Book No.)'),
        ('u|Gy', 'पुस्तक (Book)'),
        ('k|sfzs', 'प्रकाशक (Publisher)'),
        ('n]vs', 'लेखक (Author)'),
        ('ljifo', 'विषय (Subject)'),
    ]
    
    found_patterns = []
    for pattern, meaning in suspicious_patterns:
        if pattern in text:
            found_patterns.append((pattern, meaning))
    
    if found_patterns:
        print(f"\nSuspicious patterns found (likely corrupted Devanagari):")
        for pattern, meaning in found_patterns:
            print(f"  '{pattern}' -> probably means '{meaning}'")
        issues.append("legacy_font_encoding")
    
    return issues, {
        'devanagari': devanagari_count,
        'latin': latin_count,
        'extended_latin': extended_latin_count,
        'other': other_count,
        'patterns': found_patterns
    }

def create_manual_fix_suggestions(file_path, text, analysis):
    """Create suggestions for manual fixing"""
    suggestions_file = f"{file_path.stem}_fix_suggestions.txt"
    
    with open(suggestions_file, 'w', encoding='utf-8') as f:
        f.write(f"Fix Suggestions for: {file_path.name}\n")
        f.write("="*50 + "\n\n")
        
        f.write("PROBLEM ANALYSIS:\n")
        f.write(f"This file appears to have text that was originally in Devanagari script\n")
        f.write(f"but has been corrupted, likely due to:\n")
        f.write(f"1. Use of legacy fonts (like Preeti, Kantipur, etc.)\n")
        f.write(f"2. Copy-paste between different applications\n")
        f.write(f"3. Font encoding issues\n\n")
        
        if analysis['patterns']:
            f.write("DETECTED PATTERNS:\n")
            for pattern, meaning in analysis['patterns']:
                f.write(f"  '{pattern}' -> {meaning}\n")
            f.write("\n")
        
        f.write("MANUAL FIX STEPS:\n")
        f.write("1. Open the original file in MS Word\n")
        f.write("2. Select all text (Ctrl+A)\n")
        f.write("3. Change font to a Unicode Devanagari font like:\n")
        f.write("   - Mangal\n")
        f.write("   - Nirmala UI\n")
        f.write("   - Devanagari Sangam MN\n")
        f.write("4. If text still looks wrong, try:\n")
        f.write("   a. Copy text to Notepad\n")
        f.write("   b. Save as UTF-8\n")
        f.write("   c. Reopen and copy back to Word\n")
        f.write("5. Use online converters for legacy fonts:\n")
        f.write("   - Search for 'Preeti to Unicode converter'\n")
        f.write("   - Or 'Kantipur to Unicode converter'\n\n")
        
        f.write("SAMPLE TEXT (first 500 characters):\n")
        f.write("-" * 40 + "\n")
        f.write(text[:500])
        f.write("\n" + "-" * 40 + "\n")
    
    print(f"✓ Created fix suggestions: {suggestions_file}")

def create_clean_text_file(file_path, text):
    """Create a clean text file for manual processing"""
    output_file = f"{file_path.stem}_extracted_text.txt"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"Extracted text from: {file_path.name}\n")
        f.write("="*50 + "\n\n")
        f.write(text)
    
    print(f"✓ Created text file: {output_file}")

def analyze_file(file_path):
    """Analyze a single file"""
    print(f"\n{'='*60}")
    print(f"Analyzing: {file_path.name}")
    print('='*60)
    
    text = extract_text_from_docx(file_path)
    if not text:
        print("Could not extract text.")
        return
    
    print(f"File size: {len(text)} characters")
    print(f"Sample text: '{text[:100]}...'")
    
    issues, analysis = analyze_text_issues(text)
    
    if 'legacy_font_encoding' in issues:
        print(f"\n🔍 DIAGNOSIS: Legacy font encoding detected!")
        print(f"This file likely contains text that was typed using a legacy")
        print(f"Devanagari font (like Preeti, Kantipur, etc.) and needs conversion.")
        
        create_manual_fix_suggestions(file_path, text, analysis)
        create_clean_text_file(file_path, text)
        
    elif analysis['devanagari'] > 0:
        print(f"\n✓ This file appears to be correctly encoded (contains Devanagari).")
        
    else:
        print(f"\n❓ This file doesn't contain obvious Devanagari text.")
        print(f"It might be in a different language or have other encoding issues.")

def main():
    """Main function"""
    print("Font and Encoding Issue Analyzer")
    print("=================================")
    print("This tool analyzes DOCX files for font and encoding issues,")
    print("particularly with Devanagari text that appears as gibberish.")
    
    # Find DOCX files
    docx_files = [f for f in Path('.').glob('*.docx') 
                  if not f.name.startswith('~') and not 'fixed' in f.name.lower()]
    
    if not docx_files:
        print("\nNo DOCX files found.")
        return
    
    print(f"\nFound {len(docx_files)} DOCX files:")
    for i, file_path in enumerate(docx_files, 1):
        print(f"{i}. {file_path.name}")
    
    # Analyze each file
    for file_path in docx_files:
        analyze_file(file_path)
    
    print(f"\n{'='*60}")
    print("SUMMARY")
    print('='*60)
    print("Files have been analyzed for encoding issues.")
    print("\nGenerated files:")
    print("- *_fix_suggestions.txt: Manual fix instructions")
    print("- *_extracted_text.txt: Clean text for processing")
    print("\nNext steps:")
    print("1. Read the fix suggestions for each problematic file")
    print("2. Try the manual fixes in MS Word")
    print("3. For legacy fonts, use online converters")
    print("4. If you know the original font used, search for specific converters")

if __name__ == "__main__":
    main()
