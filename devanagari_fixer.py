#!/usr/bin/env python3
"""
Devanagari Text Fixer for DOCX Files

This script specifically targets the corruption where Devanagari text
has been incorrectly encoded/decoded, resulting in Latin characters.
"""

import os
from pathlib import Path
from docx import Document

def extract_text_from_docx(file_path):
    """Extract all text from a DOCX file"""
    try:
        doc = Document(file_path)
        text_parts = []
        
        for para in doc.paragraphs:
            if para.text.strip():
                text_parts.append(para.text)
        
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    if cell.text.strip():
                        text_parts.append(cell.text)
        
        return '\n'.join(text_parts)
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return None

def try_devanagari_fixes(text):
    """Try various encoding combinations to recover Devanagari text"""
    fixes = {}
    
    # Common encoding issues with Devanagari
    encoding_combinations = [
        # (wrong_encoding, correct_encoding, description)
        ('latin-1', 'utf-8', 'Latin-1 to UTF-8'),
        ('cp1252', 'utf-8', 'Windows-1252 to UTF-8'),
        ('iso-8859-1', 'utf-8', 'ISO-8859-1 to UTF-8'),
        ('utf-8', 'latin-1', 'UTF-8 to Latin-1 (reverse)'),
        ('utf-8', 'cp1252', 'UTF-8 to Windows-1252 (reverse)'),
    ]
    
    for wrong_enc, correct_enc, desc in encoding_combinations:
        try:
            # Try encoding with wrong encoding, then decoding with correct one
            fixed_bytes = text.encode(wrong_enc)
            fixed_text = fixed_bytes.decode(correct_enc)
            
            if fixed_text != text:
                # Check if this produces Devanagari characters
                devanagari_count = sum(1 for c in fixed_text if 0x0900 <= ord(c) <= 0x097F)
                if devanagari_count > 0:
                    fixes[desc] = {
                        'text': fixed_text,
                        'devanagari_chars': devanagari_count,
                        'encoding_pair': f"{wrong_enc} -> {correct_enc}"
                    }
        except (UnicodeEncodeError, UnicodeDecodeError):
            continue
    
    return fixes

def create_fixed_docx(original_path, fixed_text, output_path, fix_description):
    """Create a new DOCX file with fixed text"""
    try:
        doc = Document()
        
        # Add header information
        doc.add_heading(f'Fixed: {original_path.stem}', 0)
        doc.add_paragraph(f'Encoding fix applied: {fix_description}')
        doc.add_paragraph(f'Original file: {original_path.name}')
        doc.add_paragraph('')  # Empty line
        
        # Add the fixed text
        paragraphs = fixed_text.split('\n')
        for para_text in paragraphs:
            if para_text.strip():
                doc.add_paragraph(para_text)
        
        doc.save(output_path)
        return True
    except Exception as e:
        print(f"Error creating fixed document: {e}")
        return False

def analyze_file(file_path):
    """Analyze a single file for encoding issues"""
    print(f"\n{'='*60}")
    print(f"Analyzing: {file_path.name}")
    print('='*60)
    
    text = extract_text_from_docx(file_path)
    if not text:
        print("Could not extract text.")
        return
    
    # Show sample
    print(f"Original text sample:")
    print(f"'{text[:150]}...'")
    
    # Check current character distribution
    devanagari_chars = sum(1 for c in text if 0x0900 <= ord(c) <= 0x097F)
    latin_chars = sum(1 for c in text if ord(c) < 128)
    other_chars = len(text) - devanagari_chars - latin_chars
    
    print(f"\nCharacter analysis:")
    print(f"  Devanagari characters: {devanagari_chars}")
    print(f"  Basic Latin characters: {latin_chars}")
    print(f"  Other characters: {other_chars}")
    
    if devanagari_chars > 0:
        print("✓ File already contains Devanagari text - may be correctly encoded.")
        return
    
    # Try to fix encoding
    print("\nTrying encoding fixes...")
    fixes = try_devanagari_fixes(text)
    
    if fixes:
        print(f"Found {len(fixes)} potential fixes:")
        
        for fix_name, fix_data in fixes.items():
            print(f"\n--- {fix_name} ---")
            print(f"Devanagari characters found: {fix_data['devanagari_chars']}")
            print(f"Sample: '{fix_data['text'][:150]}...'")
            
            # Save the fix
            output_name = f"{file_path.stem}_fixed_{fix_name.replace(' ', '_').replace('-', '_')}.docx"
            if create_fixed_docx(file_path, fix_data['text'], output_name, fix_name):
                print(f"✓ Saved as: {output_name}")
    else:
        print("No encoding fixes found that produce Devanagari text.")

def main():
    """Main function"""
    print("Devanagari DOCX Encoding Fixer")
    print("===============================")
    print("This tool attempts to fix DOCX files where Devanagari text")
    print("has been corrupted due to encoding issues.")
    
    # Find DOCX files
    docx_files = [f for f in Path('.').glob('*.docx') 
                  if not f.name.startswith('~') and not 'fixed' in f.name.lower()]
    
    if not docx_files:
        print("\nNo DOCX files found.")
        return
    
    print(f"\nFound {len(docx_files)} DOCX files to analyze:")
    for i, file_path in enumerate(docx_files, 1):
        print(f"{i}. {file_path.name}")
    
    # Process each file
    for file_path in docx_files:
        analyze_file(file_path)
    
    print(f"\n{'='*60}")
    print("Analysis complete!")
    print("\nNext steps:")
    print("1. Check the generated '*_fixed_*.docx' files")
    print("2. Open them in MS Word to verify the text is readable")
    print("3. Compare with your original files")
    print("4. If the text looks correct, you can use the fixed versions")

if __name__ == "__main__":
    main()
