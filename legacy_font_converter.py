#!/usr/bin/env python3
"""
Legacy Font to Unicode Converter for DOCX Files

This script converts text from legacy Devanagari fonts (like Preeti, Kantipur)
to proper Unicode Devanagari text.
"""

import os
from pathlib import Path
from docx import Document

# Mapping for common legacy font characters to Unicode Devanagari
# This is a basic mapping - you may need to expand it based on your specific font
PREETI_TO_UNICODE = {
    # Common mappings found in your files
    'ldlt': 'मिति',
    'btf{': 'पुस्तक',
    'g+=': 'नं',
    'u|Gy': 'पुस्तक',
    'k|sfzs': 'प्रकाशक',
    'n]vs': 'लेखक',
    'ljifo': 'विषय',
    'k[i7': 'पृष्ठ',
    'k|flKt': 'प्राप्ति',
    's}lkmot': 'टिप्पणी',
    'cfldlt': 'आमिति',
    'lan': 'वार',
    
    # Numbers
    '!': '१',
    '@': '२',
    '#': '३',
    '$': '४',
    '%': '५',
    '^': '६',
    '&': '७',
    '*': '८',
    '(': '९',
    ')': '०',
    
    # Common characters
    'k': 'प',
    'u': 'उ',
    's': 'स',
    't': 'त',
    'g': 'ग',
    'f': 'फ',
    'z': 'श',
    'x': 'क्ष',
    'c': 'च',
    'v': 'व',
    'b': 'ब',
    'n': 'न',
    'm': 'म',
    'l': 'ल',
    'j': 'ज',
    'h': 'ह',
    'd': 'द',
    'r': 'र',
    'y': 'य',
    'w': 'व',
    'q': 'क',
    
    # Vowel signs
    'f{': 'ी',
    'f]': 'े',
    'f}': 'ै',
    'f|': 'ो',
    'f"': 'ौ',
    
    # Special characters
    '÷': '्',
    ':': 'ः',
    ';': 'ं',
    "'": 'ँ',
    '"': 'ू',
    
    # Common words
    'Effiff': 'भाषा',
    'gjif{': 'वर्ष',
    'gfd': 'नाम',
    '>f]t': 'स्रोत',
    
    # Punctuation
    '–': '—',
}

def convert_legacy_text(text):
    """Convert legacy font text to Unicode"""
    converted = text
    
    # Apply character mappings
    for legacy, unicode_char in PREETI_TO_UNICODE.items():
        converted = converted.replace(legacy, unicode_char)
    
    return converted

def extract_and_convert_docx(file_path):
    """Extract text from DOCX and convert legacy font characters"""
    try:
        doc = Document(file_path)
        converted_paragraphs = []
        
        # Process paragraphs
        for para in doc.paragraphs:
            if para.text.strip():
                converted_text = convert_legacy_text(para.text)
                converted_paragraphs.append(converted_text)
        
        # Process tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    if cell.text.strip():
                        converted_text = convert_legacy_text(cell.text)
                        converted_paragraphs.append(converted_text)
        
        return '\n'.join(converted_paragraphs)
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return None

def create_converted_docx(original_path, converted_text, output_path):
    """Create a new DOCX file with converted text"""
    try:
        doc = Document()
        
        # Add header
        doc.add_heading(f'Converted: {original_path.stem}', 0)
        doc.add_paragraph(f'Legacy font converted to Unicode Devanagari')
        doc.add_paragraph(f'Original file: {original_path.name}')
        doc.add_paragraph('')  # Empty line
        
        # Add converted text
        paragraphs = converted_text.split('\n')
        for para_text in paragraphs:
            if para_text.strip():
                doc.add_paragraph(para_text)
        
        doc.save(output_path)
        return True
    except Exception as e:
        print(f"Error creating converted document: {e}")
        return False

def process_file(file_path):
    """Process a single file"""
    print(f"\n{'='*60}")
    print(f"Converting: {file_path.name}")
    print('='*60)
    
    # Extract and convert
    converted_text = extract_and_convert_docx(file_path)
    if not converted_text:
        print("Could not process file.")
        return
    
    # Show before/after sample
    original_doc = Document(file_path)
    original_sample = ""
    for para in original_doc.paragraphs[:3]:  # First 3 paragraphs
        if para.text.strip():
            original_sample += para.text + "\n"
    
    converted_sample = convert_legacy_text(original_sample)
    
    print(f"BEFORE (first 200 chars):")
    print(f"'{original_sample[:200]}...'")
    print(f"\nAFTER (first 200 chars):")
    print(f"'{converted_sample[:200]}...'")
    
    # Save converted version
    output_name = f"{file_path.stem}_converted_unicode.docx"
    if create_converted_docx(file_path, converted_text, output_name):
        print(f"✓ Converted file saved as: {output_name}")
        
        # Also save as text file for verification
        text_output = f"{file_path.stem}_converted_unicode.txt"
        with open(text_output, 'w', encoding='utf-8') as f:
            f.write(f"Converted text from: {file_path.name}\n")
            f.write("="*50 + "\n\n")
            f.write(converted_text)
        print(f"✓ Text version saved as: {text_output}")
    else:
        print("✗ Failed to create converted file.")

def main():
    """Main function"""
    print("Legacy Font to Unicode Converter")
    print("=================================")
    print("This tool converts legacy Devanagari fonts to Unicode.")
    print("It works best with Preeti and similar fonts.")
    
    # Find problematic DOCX files (those with fix suggestions)
    suggestion_files = list(Path('.').glob('*_fix_suggestions.txt'))
    docx_files = []
    
    for suggestion_file in suggestion_files:
        # Get corresponding DOCX file
        docx_name = suggestion_file.name.replace('_fix_suggestions.txt', '.docx')
        docx_path = Path(docx_name)
        if docx_path.exists():
            docx_files.append(docx_path)
    
    if not docx_files:
        print("\nNo files with legacy font issues found.")
        print("Run font_encoding_fixer.py first to identify problematic files.")
        return
    
    print(f"\nFound {len(docx_files)} files to convert:")
    for i, file_path in enumerate(docx_files, 1):
        print(f"{i}. {file_path.name}")
    
    print(f"\nNote: This converter uses a basic character mapping.")
    print(f"You may need to manually adjust some conversions.")
    
    # Process each file
    for file_path in docx_files:
        process_file(file_path)
    
    print(f"\n{'='*60}")
    print("CONVERSION COMPLETE!")
    print('='*60)
    print("Generated files:")
    print("- *_converted_unicode.docx: Converted DOCX files")
    print("- *_converted_unicode.txt: Text versions for verification")
    print("\nNext steps:")
    print("1. Open the converted DOCX files in MS Word")
    print("2. Check if the text is now readable")
    print("3. Make manual corrections if needed")
    print("4. If conversion is not perfect, try online converters:")
    print("   - Search for 'Preeti to Unicode converter online'")
    print("   - Or 'Kantipur to Unicode converter online'")

if __name__ == "__main__":
    main()
