#!/usr/bin/env python3
"""
DOCX Encoding Analyzer and Fixer

This script helps analyze and fix encoding issues in DOCX files.
It can detect common encoding problems and attempt to fix them.
"""

import os
import sys
from pathlib import Path
import chardet
from docx import Document
from docx.shared import Inches
import re

def install_requirements():
    """Install required packages if not available"""
    try:
        import docx
        import chardet
    except ImportError:
        print("Installing required packages...")
        os.system("pip install python-docx chardet")
        import docx
        import chardet

def analyze_docx_text(file_path):
    """Extract and analyze text from DOCX file"""
    try:
        doc = Document(file_path)
        full_text = []

        # Extract text from paragraphs
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                full_text.append(paragraph.text)

        # Extract text from tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    if cell.text.strip():
                        full_text.append(cell.text)

        return '\n'.join(full_text)
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return None

def detect_encoding_issues(text):
    """Detect potential encoding issues in text"""
    issues = []

    # Check for common mojibake patterns
    mojibake_patterns = [
        r'[Ã¢â‚¬â„¢Ã¢â‚¬Å"Ã¢â‚¬Â]',  # Common UTF-8 to Latin-1 mojibake
        r'[Â©Â®Â°Â±Â²Â³]',  # More mojibake patterns
        r'[àáâãäåæçèéêë].*[ìíîïðñòóôõö]',  # Accented characters in wrong context
        r'[Ã][€-ÿ]',  # UTF-8 decoded as Latin-1
    ]

    for pattern in mojibake_patterns:
        if re.search(pattern, text):
            issues.append(f"Potential mojibake detected: {pattern}")

    # Check for unusual character sequences
    if any(ord(char) > 127 for char in text):
        non_ascii_chars = [char for char in text if ord(char) > 127]
        unique_chars = list(set(non_ascii_chars))[:10]  # Show first 10 unique
        issues.append(f"Non-ASCII characters found: {unique_chars}")

    return issues

def try_encoding_fixes(text):
    """Try different encoding combinations to fix mojibake"""
    fixes = {}

    # Common encoding pairs that cause issues
    encoding_pairs = [
        ('utf-8', 'latin-1'),
        ('utf-8', 'cp1252'),
        ('latin-1', 'utf-8'),
        ('cp1252', 'utf-8'),
        ('utf-8', 'iso-8859-1'),
        ('iso-8859-1', 'utf-8'),
    ]

    for wrong_encoding, correct_encoding in encoding_pairs:
        try:
            # Try to encode with wrong encoding then decode with correct one
            fixed_text = text.encode(wrong_encoding).decode(correct_encoding)
            if fixed_text != text and is_readable(fixed_text):
                fixes[f"{wrong_encoding} -> {correct_encoding}"] = fixed_text
        except (UnicodeEncodeError, UnicodeDecodeError):
            continue

    return fixes

def is_readable(text):
    """Check if text appears to be readable (basic heuristic)"""
    # Check if text has reasonable character distribution
    if len(text) == 0:
        return False

    # Count printable characters
    printable_count = sum(1 for char in text if char.isprintable() or char.isspace())
    printable_ratio = printable_count / len(text)

    # Should be mostly printable
    return printable_ratio > 0.8

def create_fixed_docx(original_path, fixed_text, output_path):
    """Create a new DOCX file with fixed text"""
    try:
        # Create new document
        doc = Document()

        # Add title
        title = doc.add_heading('Fixed Document', 0)

        # Add fixed text (split by paragraphs)
        paragraphs = fixed_text.split('\n')
        for para_text in paragraphs:
            if para_text.strip():
                doc.add_paragraph(para_text)

        # Save the document
        doc.save(output_path)
        print(f"Fixed document saved as: {output_path}")
        return True
    except Exception as e:
        print(f"Error creating fixed document: {e}")
        return False

def main():
    """Main function to analyze and fix DOCX files"""
    install_requirements()

    # Get all DOCX files in current directory
    docx_files = list(Path('.').glob('*.docx'))

    if not docx_files:
        print("No DOCX files found in current directory.")
        return

    print(f"Found {len(docx_files)} DOCX files:")
    for i, file_path in enumerate(docx_files, 1):
        print(f"{i}. {file_path.name}")

    print("\nAnalyzing files for encoding issues...\n")

    for file_path in docx_files:
        print(f"=== Analyzing: {file_path.name} ===")

        # Extract text
        text = analyze_docx_text(file_path)
        if not text:
            print("Could not extract text from file.\n")
            continue

        # Show sample of original text
        print(f"Sample text (first 200 chars):")
        print(repr(text[:200]))
        print()

        # Detect issues
        issues = detect_encoding_issues(text)
        if issues:
            print("Potential encoding issues detected:")
            for issue in issues:
                print(f"  - {issue}")
        else:
            print("No obvious encoding issues detected.")

        # Try fixes
        fixes = try_encoding_fixes(text)
        if fixes:
            print(f"\nPossible fixes found ({len(fixes)}):")
            for fix_name, fixed_text in fixes.items():
                print(f"\n  Fix: {fix_name}")
                print(f"  Sample: {repr(fixed_text[:200])}")

                # Ask user if they want to save this fix
                response = input(f"  Save this fix as '{file_path.stem}_fixed_{fix_name.replace(' -> ', '_to_')}.docx'? (y/n): ")
                if response.lower() == 'y':
                    output_name = f"{file_path.stem}_fixed_{fix_name.replace(' -> ', '_to_').replace('-', '_')}.docx"
                    create_fixed_docx(file_path, fixed_text, output_name)
        else:
            print("No automatic fixes found.")

        print("\n" + "="*50 + "\n")

if __name__ == "__main__":
    main()
