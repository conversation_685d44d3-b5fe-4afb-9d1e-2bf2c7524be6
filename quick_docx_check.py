#!/usr/bin/env python3
"""
Quick DOCX Encoding Checker

A simple script to quickly check what's inside your DOCX files
and identify potential encoding issues.
"""

import sys
import os
from pathlib import Path

def check_docx_import():
    """Check if python-docx is available"""
    try:
        from docx import Document
        return True
    except ImportError:
        print("python-docx not found. Installing...")
        os.system("pip install python-docx")
        print("Please restart the script after installation.")
        return False

def extract_text_from_docx(file_path):
    """Extract all text from a DOCX file"""
    try:
        from docx import Document
        doc = Document(file_path)
        
        text_parts = []
        
        # Get text from paragraphs
        for para in doc.paragraphs:
            if para.text.strip():
                text_parts.append(para.text)
        
        # Get text from tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    if cell.text.strip():
                        text_parts.append(cell.text)
        
        return '\n'.join(text_parts)
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return None

def analyze_text_encoding(text, filename):
    """Analyze text for encoding issues"""
    print(f"\n=== Analysis for {filename} ===")
    
    if not text:
        print("No text found or error reading file.")
        return
    
    # Basic stats
    print(f"Total characters: {len(text)}")
    print(f"Total lines: {len(text.splitlines())}")
    
    # Show first 300 characters
    print(f"\nFirst 300 characters:")
    print("-" * 40)
    print(repr(text[:300]))
    print("-" * 40)
    
    # Check for non-ASCII characters
    non_ascii = [c for c in text if ord(c) > 127]
    if non_ascii:
        unique_non_ascii = list(set(non_ascii))[:20]  # Show first 20 unique
        print(f"\nNon-ASCII characters found ({len(non_ascii)} total, {len(set(non_ascii))} unique):")
        print("Sample unique characters:", unique_non_ascii)
        
        # Show character codes
        print("Character codes (first 10):")
        for char in unique_non_ascii[:10]:
            print(f"  '{char}' -> Unicode: U+{ord(char):04X} ({ord(char)})")
    else:
        print("\nOnly ASCII characters found.")
    
    # Check for common mojibake patterns
    mojibake_indicators = [
        'Ã', 'â€', 'Â', '€™', '€œ', '€', 'Ã¡', 'Ã©', 'Ã­', 'Ã³', 'Ãº'
    ]
    
    found_mojibake = []
    for indicator in mojibake_indicators:
        if indicator in text:
            found_mojibake.append(indicator)
    
    if found_mojibake:
        print(f"\nPossible mojibake patterns found: {found_mojibake}")
        print("This suggests the text may have encoding issues.")
    else:
        print("\nNo obvious mojibake patterns detected.")

def main():
    """Main function"""
    if not check_docx_import():
        return
    
    # Find all DOCX files
    docx_files = list(Path('.').glob('*.docx'))
    
    if not docx_files:
        print("No DOCX files found in current directory.")
        return
    
    print(f"Found {len(docx_files)} DOCX files:")
    for i, file_path in enumerate(docx_files, 1):
        print(f"{i}. {file_path.name}")
    
    # Analyze each file
    for file_path in docx_files:
        text = extract_text_from_docx(file_path)
        analyze_text_encoding(text, file_path.name)
    
    print("\n" + "="*60)
    print("Analysis complete!")
    print("\nIf you see mojibake patterns or strange characters,")
    print("run the full analyzer: python docx_encoding_analyzer.py")

if __name__ == "__main__":
    main()
