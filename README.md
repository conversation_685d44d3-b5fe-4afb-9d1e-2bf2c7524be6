# DOCX Encoding Fixer

This toolkit helps you diagnose and fix encoding issues in DOCX files that display correctly in MS Word but show gibberish when copied or uploaded elsewhere.

## The Problem

You have DOCX files that:
- Display correctly when opened in Microsoft Word
- Show gibberish or strange characters when:
  - Copied and pasted into other applications
  - Uploaded to websites or online platforms
  - Opened in other word processors

This is typically caused by character encoding issues (also known as "mojibake").

## Files in This Toolkit

1. **`quick_docx_check.py`** - Quick diagnostic tool to see what's in your files
2. **`docx_encoding_analyzer.py`** - Full analyzer and fixer tool
3. **`requirements.txt`** - Python dependencies
4. **`README.md`** - This instruction file

## Quick Start

### Step 1: Install Dependencies

```bash
pip install -r requirements.txt
```

Or install manually:
```bash
pip install python-docx chardet
```

### Step 2: Quick Check

Run the quick diagnostic first:
```bash
python quick_docx_check.py
```

This will show you:
- What text is actually in your DOCX files
- Character encoding information
- Whether mojibake patterns are detected

### Step 3: Full Analysis and Fixing

If issues are detected, run the full analyzer:
```bash
python docx_encoding_analyzer.py
```

This will:
- Analyze all DOCX files in the current directory
- Detect encoding issues
- Suggest fixes
- Allow you to save corrected versions

## How It Works

### Common Encoding Issues

1. **UTF-8 to Latin-1 mojibake**: Text encoded as UTF-8 but decoded as Latin-1
2. **Windows-1252 issues**: Windows-specific encoding problems
3. **Copy-paste encoding loss**: Encoding gets corrupted during copy operations

### The Fix Process

1. **Extract text** from DOCX files
2. **Detect patterns** that indicate encoding issues
3. **Try different encoding combinations** to fix the text
4. **Create new DOCX files** with corrected text

## Example Output

When you run `quick_docx_check.py`, you might see:

```
=== Analysis for example.docx ===
Total characters: 1250
Total lines: 45

First 300 characters:
'This is some text with Ã¡Ã©Ã­Ã³Ãº characters...'

Non-ASCII characters found (25 total, 8 unique):
Sample unique characters: ['Ã', '¡', '©', '­', '³', 'º']

Possible mojibake patterns found: ['Ã']
This suggests the text may have encoding issues.
```

## Troubleshooting

### If the scripts don't work:

1. **Python not installed**: Install Python 3.7 or newer
2. **Permission errors**: Run as administrator or use `python -m pip install`
3. **Module not found**: Make sure you installed the requirements

### If no fixes are found:

1. The encoding might be correct already
2. The issue might be with fonts, not encoding
3. Try opening the file in a different application to confirm the issue

### If fixes don't look right:

1. The automatic detection might not work for your specific case
2. You might need manual encoding specification
3. Consider the original source of the text

## Manual Encoding Fix

If automatic fixes don't work, you can try manual encoding conversion:

```python
# Example: if you know the text should be UTF-8 but is being read as Latin-1
text = "your_gibberish_text_here"
fixed_text = text.encode('latin-1').decode('utf-8')
```

## Prevention

To avoid encoding issues in the future:

1. **Always use UTF-8** when creating documents
2. **Be careful with copy-paste** between different applications
3. **Check encoding** when importing text from other sources
4. **Use consistent tools** for document creation and editing

## Support

If you continue having issues:

1. Check the character codes shown in the analysis
2. Try different encoding combinations manually
3. Consider the original source of your text
4. Look up specific mojibake patterns online

## Technical Details

The tools work by:

1. **Extracting text** using the `python-docx` library
2. **Pattern matching** for common mojibake sequences
3. **Trying encoding pairs** like UTF-8 ↔ Latin-1, UTF-8 ↔ CP1252
4. **Heuristic validation** to check if fixes look readable
5. **Creating new DOCX** files with corrected text

Common encoding pairs that cause issues:
- UTF-8 → Latin-1 (most common)
- UTF-8 → CP1252 (Windows)
- ISO-8859-1 ↔ UTF-8
